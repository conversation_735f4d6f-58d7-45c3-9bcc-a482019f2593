#include "fusion.hpp"

#include "fusion_config.hpp"
#include "utils/dir.hpp"
#include "utils/file.hpp"
#include "utils/logger.hpp"
#include "utils/string_utils.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

using namespace mower_msgs::msg;

namespace fescue_iox
{

PerceptionFusion::PerceptionFusion(const std::string& conf_file, const SegRgbInitResult& seg_init_result,
                                   const CameraIntrinsicParam& camera_intrinsic, bool intrinsic_param_result)
    : conf_file_(conf_file)
{
    InitPublisher();
    InitAlg(seg_init_result, camera_intrinsic, intrinsic_param_result);
}

PerceptionFusion::~PerceptionFusion()
{
    LOG_WARN("PerceptionFusion start stop!");
    delete[] fuse_result_.debug_img_info.data;
    delete[] fuse_result_.InversePerspectMask.data;
    fuseObsRelease(fuse_handle_);
    LOG_WARN("PerceptionFusion stop success!");
}

bool PerceptionFusion::DoFusion(const segmenter_result& segments, const object_result& objects, uint64_t timestamp_ms)
{
    PreFusion(segments, objects, timestamp_ms);

    TimeDiff diff_execute;
    int result = fuseObsExecute(fuse_handle_, input_segment_result_, input_object_result_, fuse_result_);
    if (result != FUSE_SUCCESS) {
        LOG_ERROR("Perception fusion fuseObsExecute fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_PERCEPTION_FUSION_EXECUTE_ERROR_EXCEPTION);
        return false;
    }

    fuse_result_.outputtimestamp = GetSteadyClockTimestampMs();
    if (diff_execute.GetDiffMs() > 100) {
        LOG_WARN("Perception fusion fuseObsExecute cost:{} ms segment timestamp: {} system_time: {} diff: {}",
                 diff_execute.GetDiffMs(), input_segment_result_.inputtimestamp,
                 GetSteadyClockTimestampMs(), GetSteadyClockTimestampMs() - input_segment_result_.inputtimestamp);
    }

    // todo ...
    DoFusionSlopeImprove(fuse_result_, slope_result_, fuse_opt_result_);

    TimeDiff diff_post;
    PostFusion();
    LOG_DEBUG("DoFusion PostFusion cost:{} ms", diff_post.GetDiffMs());
    if (diff_post.GetDiffMs() > 50) {
        LOG_WARN("Perception fusion PostFusion cost: {} ms", diff_post.GetDiffMs());
    }

    return true;
}

FuseInitParams PerceptionFusion::GetFusionAlgoParams()
{
    return fuseObsGetParams(&fuse_handle_);
}

bool PerceptionFusion::SetFusionSegInitResult(SegRgbInitResult& seg_init_result)
{
    pixels_to_meters_ = static_cast<float>(seg_init_result.segconfig.pixelsToMeters);
    LOG_INFO("************* pixels_to_meters_ {} **************", pixels_to_meters_);
    int result = fuseObsCreatFromSegInit(&fuse_handle_, seg_init_result);
    if (result != FUSE_SUCCESS) {
        LOG_ERROR("Perception fusion fuseObsCreatFromSegInit failed! error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR, mower_msgs::msg::SocExceptionValue::ALG_PERCEPTION_FUSION_SEG_INIT_EXCEPTION);
        return false;
    }
    return true;
}

const char* PerceptionFusion::GetFusionAlgoVersion()
{
    return GetFuseVersion();
}

bool PerceptionFusion::SetFusionAlgoParams(FuseInitParams& params)
{
    int result = fuseObsSetParams(&fuse_handle_, params);
    if (result != FUSE_SUCCESS) {
        LOG_ERROR("Set perception fusion alg params fail, error code: {:X}", result);
        PublishException(SocExceptionLevel::WARNING,
                         SocExceptionValue::ALG_PERCEPTION_FUSION_PARAM_ERROR_EXCEPTION);
        return false;
    }
    LOG_INFO("Set fusion alg params success!");
    FusionAlgConfig fusion_config = Config<FusionAlgConfig>::GetConfig();
    fusion_config.debug = params.debug;
    fusion_config.img_height = params.img_height;
    fusion_config.img_width = params.img_width;
    fusion_config.cross_thresh = params.cross_thresh;
    fusion_config.undistort_enabled = params.undistort_enabled;
    fusion_config.undistort_config.camera_mode = params.undistort_params.cameraModel;
    fusion_config.undistort_config.intrinsics = params.undistort_params.intrinsics;
    fusion_config.undistort_config.distortion_coeffs = params.undistort_params.distortion_coeffs;
    fusion_config.undistort_config.resolution = params.undistort_params.resolution;
    Config<FusionAlgConfig>::SetConfig(fusion_config);
    LOG_INFO("New fusion alg params: {}", fusion_config.toString().c_str());
    return true;
}

void PerceptionFusion::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty()) {
        LOG_INFO("Perception fusion create alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path)) {
            LOG_ERROR("Perception fusion create alg config path failed!!!");
        }
    }
    if (!Config<FusionAlgConfig>::Init(conf_file_)) {
        LOG_WARN("Init perception fusion alg config parameters failed!");
    }
    FusionAlgConfig fusion_config = Config<FusionAlgConfig>::GetConfig();
    LOG_INFO("{}", fusion_config.toString().c_str());
    if (!Config<FusionAlgConfig>::SetConfig(fusion_config, true)) {
        LOG_WARN("Set perception fusion alg config parameters failed!");
    }
}

bool PerceptionFusion::InitAlg(const SegRgbInitResult& seg_init_result, const CameraIntrinsicParam& camera_intrinsic, bool intrinsic_param_result)
{
    if (!intrinsic_param_result) {
        SocExceptionValue err_code = SocExceptionValue::ALG_PERCEPTION_FUSION_GET_INTRINSIC_PARAM_ERROR_EXCEPTION;
        LOG_ERROR("Perception fusion algorithm get intrinsic param fail, error code: {:X}", static_cast<uint16_t>(err_code));
        PublishException(SocExceptionLevel::ERROR, err_code);
        return false;
    }

    InitAlgParam();

    // 记录一下bev分辨率
    pixels_to_meters_ = static_cast<float>(seg_init_result.segconfig.pixelsToMeters);
    LOG_INFO("************* pixels_to_meters_ {} **************", pixels_to_meters_);

    fuse_result_.debug_img_info.data = new uint8_t[MAX_IMG_BUFF_SIZE];
    fuse_result_.InversePerspectMask.data = new uint8_t[MAX_MASK_IMG_BUFF_SIZE];
    // Get fusion algo params
    FusionAlgConfig fusion_config = Config<FusionAlgConfig>::GetConfig();
    FuseInitParams fusion_params;
    fusion_params.img_width = fusion_config.img_width;
    fusion_params.img_height = fusion_config.img_height;
    fusion_params.cross_thresh = fusion_config.cross_thresh;
    fusion_params.debug = fusion_config.debug;
    // 是否开启undistort
    fusion_params.undistort_enabled = fusion_config.undistort_enabled;

    // 相机畸变参数
    if (intrinsic_param_result) {
        LOG_INFO("***************** Perception fusion using camera intrinsic param! ******************");
        fusion_params.undistort_params.cameraModel = camera_intrinsic.cameraModel;
        fusion_params.undistort_params.intrinsics = camera_intrinsic.intrinsics;
        fusion_params.undistort_params.distortion_coeffs = camera_intrinsic.distortion_coeffs;
        fusion_params.undistort_params.resolution = camera_intrinsic.resolution;
    } else {
        LOG_INFO("***************** Perception fusion using config file intrinsic param! ******************");
        fusion_params.undistort_params.cameraModel = fusion_config.undistort_config.camera_mode;
        fusion_params.undistort_params.intrinsics = fusion_config.undistort_config.intrinsics;
        fusion_params.undistort_params.distortion_coeffs = fusion_config.undistort_config.distortion_coeffs;
        fusion_params.undistort_params.resolution = fusion_config.undistort_config.resolution;
    }

    int result = fuseObsCreatFromStruct(&fuse_handle_, fusion_params);
    if (result != FUSE_SUCCESS) {
        LOG_ERROR("Perception fusion fuseObsCreatFromStruct failed! error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_PERCEPTION_FUSION_INIT_EXCEPTION);
        return false;
    }

    result = fuseObsCreatFromSegInit(&fuse_handle_, const_cast<SegRgbInitResult&>(seg_init_result));
    if (result != FUSE_SUCCESS) {
        LOG_ERROR("Perception fusion fuseObsCreatFromSegInit failed! error code: {:X}", result);
        PublishException(SocExceptionLevel::ERROR,
                         SocExceptionValue::ALG_PERCEPTION_FUSION_INIT_EXCEPTION);
        return false;
    }
    // pub_result_thread_ = std::thread(&PerceptionFusion::PublishResultThread, this);
    LOG_INFO("Segment object fusion alg version: {}", GetFuseVersion());
    return true;
}

void PerceptionFusion::InitPublisher()
{
    pub_fusion_result_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__PerceptionFusionResult>>("fusion_result");
    pub_fusion_mask_img_ = std::make_unique<IceoryxPublisherMower<sensor_msgs__msg__Image_iox>>("fusion_mask_image");
    pub_fusion_debug_img_ = std::make_unique<IceoryxPublisherMower<sensor_msgs__msg__Image_iox>>("fusion_debug_image");
    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void PerceptionFusion::PreSegmentResult(const segmenter_result& segments)
{
    // InversePerspectMask
    // LOG_INFO("+++++++++++ segments.inverse_perspect_mask.width {} {}", segments.inverse_perspect_mask.width, segments.inverse_perspect_mask.height);
    input_segment_result_.InversePerspectMask.width = segments.inverse_perspect_mask.width;
    input_segment_result_.InversePerspectMask.height = segments.inverse_perspect_mask.height;
    input_segment_result_.InversePerspectMask.size = segments.inverse_perspect_mask.step * segments.inverse_perspect_mask.height;
    input_segment_result_.InversePerspectMask.channels = segments.inverse_perspect_mask.step / segments.inverse_perspect_mask.width;

    // seg obstacles
    input_segment_result_.seg_obstacles.clear();
    for (size_t i = 0; i < segments.seg_obstacles.size(); i++) {
        Obstacle obstacle;
        obstacle.classID = segments.seg_obstacles[i].class_id;
        obstacle.contourType = static_cast<ContourType>(segments.seg_obstacles[i].contour_type);
        for (size_t j = 0; j < segments.seg_obstacles[i].obstacle_contour.size(); j++) {
            ObPoint point(segments.seg_obstacles[i].obstacle_contour[j].x, segments.seg_obstacles[i].obstacle_contour[j].y);
            obstacle.obstacle_contour.push_back(point);
        }
        input_segment_result_.seg_obstacles.push_back(obstacle);
    }
    // mower position
    input_segment_result_.mowerPosition.mowerpoint.x = segments.mower_point.x;
    input_segment_result_.mowerPosition.mowerpoint.y = segments.mower_point.y;

    // dead line
    input_segment_result_.deadLineLeft.start.x = segments.left_dead_line_vector.start.x;
    input_segment_result_.deadLineLeft.start.y = segments.left_dead_line_vector.start.y;
    input_segment_result_.deadLineLeft.end.x = segments.left_dead_line_vector.end.x;
    input_segment_result_.deadLineLeft.end.y = segments.left_dead_line_vector.end.y;
    input_segment_result_.deadLineRight.start.x = segments.right_dead_line_vector.start.x;
    input_segment_result_.deadLineRight.start.y = segments.right_dead_line_vector.start.y;
    input_segment_result_.deadLineRight.end.x = segments.right_dead_line_vector.end.x;
    input_segment_result_.deadLineRight.end.y = segments.right_dead_line_vector.end.y;

    // timestamp
    input_segment_result_.inputtimestamp = segments.input_timestamp;
    input_segment_result_.outputtimestamp = segments.output_timestamp;

    // status
    input_segment_result_.BoundaryStatus = static_cast<int>(segments.detect_status);
    input_segment_result_.BoundaryLeft = segments.left_detected;
    input_segment_result_.BoundaryAhead = segments.ahead_detected;
    input_segment_result_.BoundaryRight = segments.right_detected;

    // distance
    input_segment_result_.left_min_distance = segments.left_min_distance;
    input_segment_result_.ahead_min_distance = segments.ahead_min_distance;
    input_segment_result_.right_min_distance = segments.right_min_distance;

    // err code
    // input_segment_result_.errorCode = static_cast<segmenterErrorCode>(segments.error_code);

    // boundary vector
    input_segment_result_.leftBoundaryVector.start.x = segments.left_boundary_vector.start.x;
    input_segment_result_.leftBoundaryVector.start.y = segments.left_boundary_vector.start.y;
    input_segment_result_.leftBoundaryVector.end.x = segments.left_boundary_vector.end.x;
    input_segment_result_.leftBoundaryVector.end.y = segments.left_boundary_vector.end.y;
    input_segment_result_.rightBoundaryVector.start.x = segments.right_boundary_vector.start.x;
    input_segment_result_.rightBoundaryVector.start.y = segments.right_boundary_vector.start.y;
    input_segment_result_.rightBoundaryVector.end.x = segments.right_boundary_vector.end.x;
    input_segment_result_.rightBoundaryVector.end.y = segments.right_boundary_vector.end.y;

    // 草区域栅格图BEV视角
    input_segment_result_.bev_grass_region.width = segments.grass_region_result.width;
    input_segment_result_.bev_grass_region.height = segments.grass_region_result.height;
    input_segment_result_.bev_grass_region.resolution = segments.grass_region_result.resolution;
    int cells_size = std::min((int)(segments.grass_region_result.cells_array.size()), MAX_CELL_NUM);
    // LOG_INFO("PerceptionFusion::PreSegmentResult cells_size: {}", cells_size);
    for (int i = 0; i < cells_size; i++) {
        input_segment_result_.bev_grass_region.cells_array[i] = static_cast<GrassCellType>(segments.grass_region_result.cells_array[i]);
    }
}

void PerceptionFusion::PreObjectResult(const object_result& objects)
{
    input_object_result_.clear();
    for (size_t i = 0; i < objects.size(); i++) {
        DetectionResult result;
        result.classID = objects[i].object_id;
        LOG_DEBUG("PreObjectResult classID: {}", result.classID);
        for (size_t j = 0; j < objects[i].points_array.size(); j++) {
            ObPoint point(objects[i].points_array[j].pos_x, objects[i].points_array[j].pos_y);
            result.objectContour.push_back(point);
        }
        input_object_result_.push_back(result);
    }
}

bool PerceptionFusion::PreFusion(const segmenter_result& segments, const object_result& objects, uint64_t timestamp_ms)
{
    // segmenter input data
    PreSegmentResult(segments);

    // object detect data
    PreObjectResult(objects);

    fuse_result_.obstacleInfo.clear();
    fuse_result_.obstacleNum = 0;
    fuse_result_.obstacleStatus = ObstacleStatus::SYSTEM_NO_OBSTACLE;

    sec_ = timestamp_ms / 1000; // ms->s;
    nanosec_ = (timestamp_ms % 1000) * 1000 * 1000;
    timestamp_ms_ = timestamp_ms;

    return true;
}

void PerceptionFusion::PostFusion()
{
    if (last_fuse_timestamp_ms_ != fuse_result_.inputtimestamp) {
        last_fuse_timestamp_ms_ = fuse_result_.inputtimestamp;
        PublishResult();
    }

    PublishImage(pub_fusion_debug_img_, "fusion_debug_img", "bgr8", fuse_result_.debug_img_info);
    PublishImage(pub_fusion_mask_img_, "fusion_mask_img", "mono8", fuse_result_.InversePerspectMask);
}

void PerceptionFusion::PublishResult()
{
    if (!pub_fusion_result_ || !pub_fusion_result_->hasSubscribers()) {
        return;
    }

    fescue_msgs__msg__PerceptionFusionResult result;
    auto* msg = &result;
    TimeDiff diff;

    msg->status = static_cast<fescue_msgs_enum__FusionObjectStatus>(fuse_result_.obstacleStatus);
    // msg->error_code = static_cast<int>(fuse_result_.errorCode);
    msg->timestamp = fuse_result_.inputtimestamp;
    msg->boundary_state = fuse_result_.BoundaryStatus;
    msg->left_boundary = fuse_result_.BoundaryLeft;
    msg->ahead_boundary = fuse_result_.BoundaryAhead;
    msg->right_boundary = fuse_result_.BoundaryRight;
    msg->left_min_distance = fuse_result_.left_min_distance;
    msg->ahead_min_distance = fuse_result_.ahead_min_distance;
    msg->right_min_distance = fuse_result_.right_min_distance;

    msg->object_num = fuse_result_.obstacleNum;
    size_t object_size = std::min(IOX_MAX_OBJECT_NUM, (int)(fuse_result_.obstacleInfo.size()));
    for (size_t i = 0; i < object_size; i++) {
        fescue_msgs__msg__FusionObjectInfo object_info;
        object_info.class_id = fuse_result_.obstacleInfo[i].classID;
        object_info.pos_left = fuse_result_.obstacleInfo[i].obstaclePosition.POSITION_LEFT;
        object_info.pos_ahead = fuse_result_.obstacleInfo[i].obstaclePosition.POSITION_AHEAD;
        object_info.pos_right = fuse_result_.obstacleInfo[i].obstaclePosition.POSITION_RIGHT;
        size_t object_contour_size = std::min(IOX_MAX_OBJECT_CONTOUR_POINT_NUM, (int)(fuse_result_.obstacleInfo[i].objectContour.size()));
        for (size_t j = 0; j < object_contour_size; j++) {
            geometry_msgs__msg__Point_iox point;
            point.x = fuse_result_.obstacleInfo[i].objectContour[j].x;
            point.y = fuse_result_.obstacleInfo[i].objectContour[j].y;
            object_info.contour_array.push_back(point);
        }
        msg->object_array.push_back(object_info);
    }
    // 分割算法处理完成当前图片后，记录当前系统时间戳
    msg->output_timestamp = fuse_result_.outputtimestamp;
    // 在BEV视角下，距离割草机最近的障碍物坐标点
    msg->min_dist_point.x = fuse_result_.min_dist_point.x;
    msg->min_dist_point.y = fuse_result_.min_dist_point.y;
    // 割草机位置
    msg->mower_point.x = fuse_result_.mowerPosition.mowerpoint.x;
    msg->mower_point.y = fuse_result_.mowerPosition.mowerpoint.y;
    // 分辨率
    msg->pixels_to_meters = pixels_to_meters_;
    // BEV 投影 MASK
    msg->inverse_perspect_mask.encoding = "mono8";
    // LOG_INFO("*********** fuse_result_.InversePerspectMask.width {} {}", fuse_result_.InversePerspectMask.width,
    //          fuse_result_.InversePerspectMask.height);
    msg->inverse_perspect_mask.width = fuse_result_.InversePerspectMask.width;
    msg->inverse_perspect_mask.height = fuse_result_.InversePerspectMask.height;
    msg->inverse_perspect_mask.is_bigendian = false;
    msg->inverse_perspect_mask.step = fuse_result_.InversePerspectMask.width * fuse_result_.InversePerspectMask.channels;
    size_t img_size = std::min<size_t>(msg->inverse_perspect_mask.step * msg->inverse_perspect_mask.height, IOX_IMAGE_DATA_MAX);
    msg->inverse_perspect_mask.data.resize(img_size);
    memcpy(msg->inverse_perspect_mask.data.data(), fuse_result_.InversePerspectMask.data, img_size);
    // bev_grass_region
    msg->bev_grass_region.width = fuse_result_.bev_grass_region.width;
    msg->bev_grass_region.height = fuse_result_.bev_grass_region.height;
    msg->bev_grass_region.resolution = fuse_result_.bev_grass_region.resolution;
    int temp_size = msg->bev_grass_region.width * msg->bev_grass_region.height;
    int cells_size = std::min(IOX_MAX_CELL_NUM, temp_size);
    for (int i = 0; i < cells_size; i++) {
        uint8_t cell = static_cast<uint8_t>(fuse_result_.bev_grass_region.cells_array[i]);
        msg->bev_grass_region.cells_array.push_back(cell);
    }
    // bev_grass_region_opt
    msg->opt_status = fuse_opt_result_.opt_status;
    msg->bev_grass_region_opt.width = fuse_opt_result_.opt_result.width;
    msg->bev_grass_region_opt.height = fuse_opt_result_.opt_result.height;
    msg->bev_grass_region_opt.resolution = fuse_opt_result_.opt_result.resolution;
    temp_size = msg->bev_grass_region_opt.width * msg->bev_grass_region_opt.height;
    cells_size = std::min(IOX_MAX_CELL_NUM, temp_size);
    for (int i = 0; i < cells_size; i++) {
        uint8_t cell = static_cast<uint8_t>(fuse_opt_result_.opt_result.cells_array[i]);
        msg->bev_grass_region_opt.cells_array.push_back(cell);
    }
    pub_fusion_result_->publish(result);

    if (diff.GetDiffMs() > 50) {
        LOG_WARN("Perception fusion publish fusion result cost time: {}ms", diff.GetDiffMs());
    }
}

void PerceptionFusion::PreSlopeResult(const mower_msgs::msg::LawnmowerSlopeStatus& data)
{
    slope_result_ = data;
}

void PerceptionFusion::PublishImage(const std::unique_ptr<IceoryxPublisherMower<sensor_msgs__msg__Image_iox>>& publisher,
                                    const std::string& frame_id,
                                    const std::string& encoding,
                                    const SegImageBuffer& image)
{
    if (!publisher || !publisher->hasSubscribers() || image.size <= 0) {
        return;
    }
    sensor_msgs__msg__Image_iox img_msg;
    auto* msg = &img_msg;
    TimeDiff diff;
    msg->header.stamp.sec = sec_;
    msg->header.stamp.nanosec = nanosec_;
    msg->header.frame_id.unsafe_assign(frame_id.c_str());
    msg->height = image.height;
    msg->width = image.width;
    msg->encoding.unsafe_assign(encoding.c_str());
    msg->is_bigendian = false;
    msg->step = msg->width * image.channels;
    size_t img_size = std::min<size_t>(msg->step * msg->height, IOX_IMAGE_DATA_MAX);
    msg->data.resize(img_size);
    memcpy(msg->data.data(), image.data, img_size);
    publisher->publish(img_msg);
    if (diff.GetDiffMs() > 50) {
        LOG_WARN("Perception fusion publish {} cost time: {}ms", frame_id.c_str(), diff.GetDiffMs());
    }
}

void PerceptionFusion::PublishException(mower_msgs::msg::SocExceptionLevel level,
                                        mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_) {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetSteadyClockTimestampMs();
        exception.node_name = "perception_segment_object_fusion_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

} // namespace fescue_iox
